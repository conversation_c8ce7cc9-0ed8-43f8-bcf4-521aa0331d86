import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

console.log('API URL:', import.meta.env.VITE_API_URL || 'http://localhost:3001/api')

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
      toast.error('Session expired. Please login again.')
    } else if (error.response?.status === 403) {
      toast.error('Access denied')
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.')
    }
    
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  verifyToken: () => api.get('/auth/verify'),
  getQRCode: () => api.get('/auth/qr'),
}

// Status API
export const statusAPI = {
  getStatus: () => api.get('/status'),
  getHealth: () => api.get('/status/health'),
}

// Messages API
export const messagesAPI = {
  send: (data) => api.post('/messages/send', data),
  sendMedia: (formData) => api.post('/messages/send-media', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  getMessages: (chatId, limit = 50) => api.get(`/messages/${chatId}?limit=${limit}`),
  schedule: (data) => api.post('/messages/schedule', data),
  getScheduled: () => api.get('/messages/scheduled'),
  cancelScheduled: (id) => api.delete(`/messages/scheduled/${id}`),
}

// Contacts API
export const contactsAPI = {
  getAll: (params = {}) => api.get('/contacts', { params }),
  getById: (id) => api.get(`/contacts/${id}`),
  check: (phoneNumber) => api.post('/contacts/check', { phoneNumber }),
  getProfilePicture: (id) => api.get(`/contacts/${id}/profile-picture`),
  block: (contactId) => api.post('/contacts/block', { contactId }),
  unblock: (contactId) => api.post('/contacts/unblock', { contactId }),
}

// Groups API
export const groupsAPI = {
  getAll: (params = {}) => api.get('/groups', { params }),
  getById: (id) => api.get(`/groups/${id}`),
  create: (data) => api.post('/groups', data),
  update: (id, data) => api.put(`/groups/${id}`, data),
  addParticipants: (id, participants) => api.post(`/groups/${id}/participants`, { participants }),
  removeParticipants: (id, participants) => api.delete(`/groups/${id}/participants`, { data: { participants } }),
  leave: (id) => api.post(`/groups/${id}/leave`),
}

// Utility functions
export const uploadFile = async (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)

  return api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    },
  })
}

export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    toast.error('Failed to download file')
    throw error
  }
}

// Helper function to format phone numbers
export const formatPhoneNumber = (phoneNumber) => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '')
  
  // Add country code if missing (assuming US)
  if (cleaned.length === 10) {
    return `1${cleaned}`
  }
  
  return cleaned
}

// Helper function to format chat ID
export const formatChatId = (phoneNumber, isGroup = false) => {
  const formatted = formatPhoneNumber(phoneNumber)
  return `${formatted}@${isGroup ? 'g' : 'c'}.us`
}

// Helper function to extract phone number from chat ID
export const extractPhoneNumber = (chatId) => {
  return chatId.split('@')[0]
}

// Helper function to check if chat ID is a group
export const isGroupChat = (chatId) => {
  return chatId.includes('@g.us')
}

// Error handling helper
export const handleAPIError = (error, defaultMessage = 'An error occurred') => {
  const message = error.response?.data?.error || error.message || defaultMessage
  toast.error(message)
  return message
}

// Request retry helper
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
    }
  }
}

export default api
